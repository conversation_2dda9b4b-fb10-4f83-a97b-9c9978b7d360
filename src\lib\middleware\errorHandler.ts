import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { 
  validationErrorResponse, 
  businessErrorResponse,
  systemErrorResponse 
} from '@/lib/utils/apiResponse';
import { ErrorCode, ApiError, createApiError } from '@/lib/constants/errorCodes';

/**
 * API路由处理函数类型
 */
export type ApiHandler = (
  request: NextRequest,
  context?: { params?: any }
) => Promise<NextResponse> | NextResponse;

/**
 * 错误处理中间件选项
 */
export interface ErrorHandlerOptions {
  /** 是否记录错误日志 */
  enableLogging?: boolean;
  /** 自定义错误消息 */
  defaultErrorMessage?: string;
  /** 是否在开发环境显示详细错误信息 */
  showDetailInDev?: boolean;
}

/**
 * 高阶函数：为API路由添加统一错误处理
 * @param handler API路由处理函数
 * @param options 错误处理选项
 */
export function withErrorHandler(
  handler: <PERSON>pi<PERSON><PERSON><PERSON>,
  options: ErrorHandlerOptions = {}
): ApiHandler {
  const {
    enableLogging = true,
    defaultErrorMessage = '服务器内部错误',
    showDetailInDev = true,
  } = options;

  return async (request: NextRequest, context?: { params?: any }) => {
    try {
      // 执行原始处理函数
      const result = await handler(request, context);
      return result;
    } catch (error) {
      // 记录错误日志
      if (enableLogging) {
        logError(error, request, context);
      }

      // 处理不同类型的错误
      return handleErrorByType(error, defaultErrorMessage, showDetailInDev);
    }
  };
}

/**
 * 根据错误类型处理错误
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @param showDetailInDev 是否在开发环境显示详细信息
 */
function handleErrorByType(
  error: unknown,
  defaultMessage: string,
  showDetailInDev: boolean
): NextResponse {
  // 处理Zod验证错误
  if (error instanceof ZodError) {
    return handleZodError(error);
  }

  // 处理自定义API错误
  if (error instanceof ApiError) {
    return businessErrorResponse(error.code, error.message, error.details);
  }

  // 处理标准Error
  if (error instanceof Error) {
    // 在开发环境显示详细错误信息
    if (showDetailInDev && process.env.NODE_ENV === 'development') {
      return systemErrorResponse(error, `${defaultMessage}: ${error.message}`);
    }
    return systemErrorResponse(error, defaultMessage);
  }

  // 处理未知错误
  return systemErrorResponse(undefined, defaultMessage);
}

/**
 * 处理Zod验证错误
 * @param error ZodError实例
 */
function handleZodError(error: ZodError): NextResponse {
  // 格式化Zod错误信息
  const formattedErrors = error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));

  // 生成用户友好的错误消息
  const userMessage = generateUserFriendlyMessage(error.errors);

  return validationErrorResponse(formattedErrors, userMessage);
}

/**
 * 生成用户友好的验证错误消息
 * @param errors Zod错误数组
 */
function generateUserFriendlyMessage(errors: any[]): string {
  if (errors.length === 0) return '参数验证失败';
  
  const firstError = errors[0];
  const fieldName = firstError.path.join('.');
  
  switch (firstError.code) {
    case 'invalid_type':
      return `字段 ${fieldName} 类型不正确`;
    case 'too_small':
      return `字段 ${fieldName} 长度不足`;
    case 'too_big':
      return `字段 ${fieldName} 长度超出限制`;
    case 'invalid_string':
      return `字段 ${fieldName} 格式不正确`;
    default:
      return `字段 ${fieldName} 验证失败: ${firstError.message}`;
  }
}

/**
 * 记录结构化错误日志
 * @param error 错误对象
 * @param request 请求对象
 * @param context 上下文信息
 */
function logError(
  error: unknown,
  request: NextRequest,
  context?: { params?: any }
): void {
  const timestamp = new Date().toISOString();
  const url = request.url;
  const method = request.method;
  const userAgent = request.headers.get('user-agent') || 'Unknown';
  
  const logData = {
    timestamp,
    level: 'ERROR',
    url,
    method,
    userAgent,
    params: context?.params,
    error: {
      name: error instanceof Error ? error.name : 'UnknownError',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: error instanceof ApiError ? error.code : undefined,
    },
  };

  // 在生产环境中，这里应该发送到日志服务
  console.error('API错误:', JSON.stringify(logData, null, 2));
}

/**
 * 验证请求体的中间件
 * @param schema Zod验证模式
 * @param handler API处理函数
 */
export function withValidation<T>(
  schema: any,
  handler: (request: NextRequest, validatedData: T, context?: { params?: any }) => Promise<NextResponse>
): ApiHandler {
  return withErrorHandler(async (request: NextRequest, context?: { params?: any }) => {
    try {
      const body = await request.json();
      const validatedData = schema ? schema.parse(body) : body;
      return await handler(request, validatedData, context);
    } catch (error: any) {
      if (error instanceof ZodError) {
        throw error; // 让withErrorHandler处理
      }
      throw createApiError(error.code || ErrorCode.INVALID_PARAMETERS, error.message || '请求体格式错误');
    }
  });
}

/**
 * 验证查询参数的中间件
 * @param schema Zod验证模式
 * @param handler API处理函数
 */
export function withQueryValidation<T>(
  schema: any,
  handler: (request: NextRequest, validatedQuery: T, context?: { params?: any }) => Promise<NextResponse>
): ApiHandler {
  return withErrorHandler(async (request: NextRequest, context?: { params?: any }) => {
    const url = new URL(request.url);
    const queryParams: Record<string, any> = {};
    
    // 转换查询参数
    url.searchParams.forEach((value, key) => {
      // 尝试转换数字类型
      if (!isNaN(Number(value)) && value !== '') {
        queryParams[key] = Number(value);
      } else if (value === 'true' || value === 'false') {
        queryParams[key] = value === 'true';
      } else {
        queryParams[key] = value;
      }
    });

    const validatedQuery = schema ? schema.parse(queryParams) : queryParams;
    return await handler(request, validatedQuery, context);
  });
}

/**
 * 业务逻辑断言函数
 * @param condition 条件
 * @param errorCode 错误码
 * @param message 错误消息
 */
export function assert(
  condition: boolean,
  errorCode: ErrorCode,
  message?: string
): asserts condition {
  if (!condition) {
    throw createApiError(errorCode, message);
  }
}

/**
 * 检查资源是否存在
 * @param resource 资源对象
 * @param errorCode 错误码
 * @param message 错误消息
 */
export function assertExists<T>(
  resource: T | null | undefined,
  errorCode: ErrorCode = ErrorCode.RESOURCE_NOT_FOUND,
  message?: string
): asserts resource is T {
  if (!resource) {
    throw createApiError(errorCode, message);
  }
} 