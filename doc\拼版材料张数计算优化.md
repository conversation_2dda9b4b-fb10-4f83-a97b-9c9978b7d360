# 拼版材料张数计算优化

## 问题描述

之前的拼版计算逻辑中，`sheetsNeeded` 只是计算单张材料的拼版情况，没有考虑实际需要生产的盒子数量。这导致拼版结果显示的材料张数不是实际需要的张数。

## 解决方案

### 1. 修改 ImpositionParams 接口

在 `src/app/admin/box/calculation/types/packaging.ts` 中添加了两个新参数：

```typescript
export interface ImpositionParams {
  // ... 其他参数

  // 生产数量参数
  quantity?: number;                 // 盒子数量，用于计算实际需要的材料张数

  // ... 其他参数
}
```

### 2. 修改计算引擎传参

在 `src/app/admin/box/calculation/utils/calculationEngine.ts` 中，调用拼版计算时传递盒子数量：

```typescript
const impositionParams = {
  // ... 其他参数
  quantity: basicInfo.quantity || 1000 // 传递盒子数量
};
```

### 3. 修改拼版计算逻辑

在 `src/app/admin/box/calculation/utils/packagingEngine.ts` 中，修改 `calculateImposition` 方法：

```typescript
// 计算实际需要的材料张数（基于盒子数量）
const quantity = params.quantity || 1000;

// 基础材料张数：根据盒子数量和拼版数量计算
const baseSheetsNeeded = Math.ceil(quantity / totalImposition);

// 初始化最终材料张数
let sheetsNeeded = baseSheetsNeeded;
```

### 4. 更新显示界面

在 `src/app/admin/box/calculation/components/ImpositionLogicDisplay.tsx` 中，更新显示文本：

```typescript
<Descriptions.Item label="实际需要张数">
  <Text strong style={{ color: '#1890ff' }}>{result.sheetsNeeded}</Text> 张
</Descriptions.Item>
```

## 计算逻辑

### 基础计算公式

```
实际需要材料张数 = ⌈盒子数量 ÷ 每张材料拼版数量⌉
```

### 示例计算

假设：
- 盒子数量：5000个
- 每张材料拼版数量：8个/张

计算过程：
1. 实际需要材料张数 = ⌈5000 ÷ 8⌉ = ⌈625⌉ = 625张

### 尺寸限制处理

如果材料尺寸超出打印机或材料规格限制，会进一步调整：

```typescript
if (exceedsWidth || exceedsLength) {
  // 如果单张材料超出限制，需要重新计算
  const maxSingleSheetArea = effectiveMaxWidth * effectiveMaxLength;
  const sheetsPerOriginalSheet = Math.ceil(finalMaterialArea / maxSingleSheetArea);
  sheetsNeeded = baseSheetsNeeded * sheetsPerOriginalSheet;
}
```

## 优化效果

1. **准确性提升**：拼版结果现在显示的是实际需要的材料张数，而不是单张材料的拼版情况
2. **精确计算**：直接按照盒子数量和拼版数量计算，不包含损耗，提供精确的材料需求
3. **清晰显示**：界面上明确标注"实际需要张数"，避免误解
4. **简化逻辑**：移除损耗率计算，让拼版结果更加直观和准确

## 调试信息

系统会输出详细的计算日志：

```
[材料张数计算] 部件组名称: {
  quantity: 5000,
  totalImposition: 8,
  baseSheetsNeeded: 625,
  finalSheetsNeeded: 625
}
```

这样可以方便调试和验证计算结果的正确性。

## 问题修复

### 参数传递错误修复

在初始实现中发现了一个参数传递错误：在 `PackagingStep.tsx` 中调用 `optimizeImposition` 时，没有传递 `quantity` 参数，导致拼版计算无法获取盒子数量。

**修复方法**：在 `PackagingStep.tsx` 的 `impositionParams` 中添加 `quantity` 参数：

```typescript
const impositionParams = {
  // ... 其他参数
  quantity: state.basicInfo.quantity || 1000, // 传递盒子数量
  // ... 其他参数
};
```

## 材料汇总显示优化

### 新增功能：按材料分组显示总张数

在拼版结果的总体统计中，新增了按选定材料分组显示总张数的功能。

#### 实现方式

1. **数据传递**：在 `PackagingStep.tsx` 中向 `ImpositionLogicDisplay` 传递 `partMaterialConfigs` 参数
2. **分组计算**：根据选定的材料名称和规格进行分组统计
3. **界面显示**：在拼版汇总卡片中优先显示材料需求汇总

#### 显示效果

```
材料需求汇总：
[铜版纸] [正度] 625张 (2个部件组)
[灰板纸] [2.0mm] 313张 (1个部件组)
```

#### 代码实现

**接口扩展**：
```typescript
interface ImpositionLogicDisplayProps {
  // ... 其他属性
  partMaterialConfigs?: Record<string, any>; // 部件材料配置
}
```

**分组统计逻辑**：
```typescript
const materialStats = React.useMemo(() => {
  if (!partMaterialConfigs) return null;

  const materialGroups: Record<string, {
    materialName: string;
    materialSpec: string;
    totalSheets: number;
    results: ImpositionResult[];
  }> = {};

  impositionResults.forEach(result => {
    const partGroupId = result.partGroup.id;
    const config = partMaterialConfigs[partGroupId];

    if (config && config.materialName && config.materialSpec) {
      const key = `${config.materialName}_${config.materialSpec}`;

      if (!materialGroups[key]) {
        materialGroups[key] = {
          materialName: config.materialName,
          materialSpec: config.materialSpec,
          totalSheets: 0,
          results: []
        };
      }

      materialGroups[key].totalSheets += result.sheetsNeeded;
      materialGroups[key].results.push(result);
    }
  });

  return Object.values(materialGroups);
}, [impositionResults, partMaterialConfigs]);
```

这样修改后，拼版结果显示的材料张数就是实际需要使用的张数，按照盒子数量参与计算，更加准确和直观。同时，总体统计会根据上方选定的材料进行分组显示，让用户清楚地看到每种材料需要多少张。
