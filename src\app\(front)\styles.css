/* 前台页面专用样式 - 使用 Ant Design 组件的传统电商风格 */

/* 全局样式重置 */
.front-layout {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* 轮播图样式 */
.ant-carousel .slick-dots {
  bottom: 10px;
}

.ant-carousel .slick-dots li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
}

.ant-carousel .slick-dots li.slick-active button {
  background: #1890ff;
}

/* Ant Design 组件样式调整 */
.ant-card {
  border-radius: 0 !important;
}

.ant-card-head {
  border-radius: 0 !important;
}

.ant-card-body {
  border-radius: 0 !important;
}

.ant-btn {
  border-radius: 3px !important;
}

.ant-input {
  border-radius: 3px !important;
}

/* 分类菜单样式 */
.category-menu .ant-menu-item {
  padding-left: 15px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-bottom: 1px solid #f0f0f0;
}

.category-menu .ant-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.category-menu .ant-menu-item a {
  color: #666 !important;
}

.category-menu .ant-menu-item:hover a {
  color: #1890ff !important;
}

/* 页脚链接样式 */
.ant-typography a {
  color: inherit;
  text-decoration: none;
}

.ant-typography a:hover {
  color: #fff !important;
}

/* 传统电商网站样式 */
.traditional-layout {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
}

/* 简单的悬浮效果 */
.simple-hover {
  transition: background-color 0.2s ease;
}

.simple-hover:hover {
  background-color: #f0f8ff;
}

/* 传统边框样式 */
.traditional-border {
  border: 1px solid #e0e0e0;
}

/* 传统按钮样式 */
.traditional-button {
  background: #1890ff;
  color: #fff;
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
}

.traditional-button:hover {
  background: #40a9ff;
}


/* 基础文字样式 */
.text-small {
  font-size: 12px;
}

.text-normal {
  font-size: 13px;
}

.text-large {
  font-size: 14px;
}

/* 基础颜色 */
.text-primary {
  color: #1890ff;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 基础间距 */
.margin-small {
  margin: 5px;
}

.padding-small {
  padding: 5px;
}

.margin-normal {
  margin: 10px;
}

.padding-normal {
  padding: 10px;
}

/* 简化的导航栏样式 */
.front-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

/* 简化的页脚样式 */
.front-footer {
  background: #333;
  color: #ccc;
}

/* 响应式优化 - 保持基本的移动端适配 */
@media (max-width: 768px) {
  .traditional-layout {
    padding: 0 10px;
  }

  /* 移动端隐藏侧边栏，改为垂直布局 */
  .mobile-stack {
    flex-direction: column;
  }

  .mobile-full-width {
    width: 100%;
  }
}

/* 基础滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}
