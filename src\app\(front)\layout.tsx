import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '艺创包装 - 专业纸箱包装定制服务',
  description: '艺创包装提供专业的纸箱包装定制服务，包括各种盒型设计、印刷工艺、现货产品等。24小时热线：18638728164',
  keywords: '纸箱包装,包装定制,盒型设计,印刷工艺,包装报价,现货包装,屋脊样式,飞机盒,手提袋,礼品盒',
  authors: [{ name: '艺创包装' }],
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1',
  robots: 'index, follow',
  alternates: {
    canonical: 'https://www.ycbz.com/front',
  },
  openGraph: {
    title: '艺创包装 - 专业纸箱包装定制服务',
    description: '艺创包装提供专业的纸箱包装定制服务，包括各种盒型设计、印刷工艺、现货产品等。',
    type: 'website',
    locale: 'zh_CN',
    url: 'https://www.ycbz.com/front',
    siteName: '艺创包装',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: '艺创包装 - 专业纸箱包装定制服务',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '艺创包装 - 专业纸箱包装定制服务',
    description: '艺创包装提供专业的纸箱包装定制服务，包括各种盒型设计、印刷工艺、现货产品等。',
    images: ['/images/og-image.jpg'],
  },
  other: {
    'baidu-site-verification': 'your-baidu-verification-code',
    'google-site-verification': 'your-google-verification-code',
  },
};

export default function FrontLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "艺创包装",
    "description": "专业纸箱包装定制服务提供商",
    "url": "https://www.ycbz.com",
    "logo": "https://www.ycbz.com/images/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+86-18638728164",
      "contactType": "customer service",
      "availableLanguage": "Chinese"
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "郑州市",
      "addressRegion": "河南省",
      "addressCountry": "CN"
    },
    "sameAs": [
      "https://www.ycbz.com"
    ]
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />

      {/* 预加载关键资源 */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

      <div className="front-layout">
        {children}
      </div>
    </>
  );
}
