/**
 * 统一错误码系统
 * 错误码分类：
 * 1000-1999: 通用错误
 * 2000-2999: 认证授权错误  
 * 3000-3999: 业务逻辑错误
 * 5000-5999: 系统错误
 */

// 错误码枚举
export enum ErrorCode {
  // 通用错误 (1000-1999)
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  INTERNAL_SERVER_ERROR = 500,

  // 通用业务错误 (1000-1999)
  INVALID_PARAMETERS = 1000, // 参数验证失败
  MISSING_REQUIRED_FIELD = 1001, // 缺少必填字段
  INVALID_FORMAT = 1002, // 格式不正确
  PARAMETER_TOO_LONG = 1003, // 参数长度超出限制
  PARAMETER_TOO_SHORT = 1004, // 参数长度不足
  INVALID_DATE_FORMAT = 1005, // 日期格式不正确
  INVALID_NUMBER_FORMAT = 1006, // 数字格式不正确
  DUPLICATE_ENTRY = 1007, // 数据已存在
  RESOURCE_NOT_FOUND = 1008, // 资源不存在
  OPERATION_FAILED = 1009, // 操作失败

  // 认证授权错误 (2000-2999)
  LOGIN_REQUIRED = 2000, // 请先登录
  INVALID_TOKEN = 2001, // 无效的访问令牌
  TOKEN_EXPIRED = 2002, // 访问令牌已过期
  INSUFFICIENT_PERMISSIONS = 2003, // 权限不足
  ACCOUNT_DISABLED = 2004, // 账号已禁用
  ACCOUNT_LOCKED = 2005, // 账号已锁定

  // 盒型业务错误 (3000-3099)
  BOX_NAME_EXISTS = 3000, // 盒型名称已存在
  BOX_NOT_FOUND = 3001, // 盒型不存在
  BOX_CANNOT_DELETE_PUBLISHED = 3002, // 已发布的盒型不能删除
  BOX_INVALID_TYPE = 3003, // 盒型类型无效
  BOX_PROCESSING_FEE_INVALID = 3004, // 加工费设置无效
  BOX_ATTRIBUTE_INVALID = 3005, // 盒型属性设置无效
  BOX_PART_INVALID = 3006, // 盒型部件设置无效
  BOX_FORMULA_INVALID = 3007, // 盒型公式设置无效
  BOX_IMAGE_INVALID = 3008, // 盒型图片格式无效
  BOX_IMAGE_TOO_LARGE = 3009, // 盒型图片尺寸过大

  // 自定义公式业务错误 (3100-3199)
  FORMULA_NAME_EXISTS = 3100, // 公式名称已存在
  FORMULA_NOT_FOUND = 3101, // 公式不存在
  FORMULA_EXPRESSION_INVALID = 3102, // 公式表达式无效
  FORMULA_CANNOT_DELETE_IN_USE = 3103, // 公式正在使用中，无法删除
  FORMULA_ATTRIBUTE_INVALID = 3104, // 公式属性设置无效
  FORMULA_EVALUATION_FAILED = 3105, // 公式计算失败

  // 材料业务错误 (3200-3299)
  MATERIAL_NAME_EXISTS = 3200, // 材料名称已存在
  MATERIAL_NOT_FOUND = 3201, // 材料不存在
  MATERIAL_PRICE_INVALID = 3202, // 材料价格设置无效
  MATERIAL_UNIT_INVALID = 3203, // 材料单位设置无效
  MATERIAL_CATEGORY_INVALID = 3204, // 材料分类无效
  MATERIAL_CANNOT_DELETE_IN_USE = 3205, // 材料正在使用中，无法删除
  MATERIAL_SPECIFICATION_INVALID = 3206, // 材料规格设置无效

  // 系统错误 (5000-5999)
  DATABASE_ERROR = 5000, // 数据库操作失败
  FILE_SYSTEM_ERROR = 5001, // 文件系统操作失败
  NETWORK_ERROR = 5002, // 网络连接失败
  EXTERNAL_SERVICE_ERROR = 5003, // 外部服务调用失败
  MEMORY_ERROR = 5004, // 内存不足
  TIMEOUT_ERROR = 5005, // 操作超时
  UNKNOWN_ERROR = 5999, // 未知错误
}

// 错误码对应的中文消息
export const ErrorMessages: Record<ErrorCode, string> = {
  // HTTP状态码
  [ErrorCode.SUCCESS]: '操作成功',
  [ErrorCode.BAD_REQUEST]: '请求参数错误',
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.FORBIDDEN]: '访问被拒绝',
  [ErrorCode.NOT_FOUND]: '资源不存在',
  [ErrorCode.METHOD_NOT_ALLOWED]: '请求方法不允许',
  [ErrorCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',

  // 通用业务错误
  [ErrorCode.INVALID_PARAMETERS]: '参数验证失败',
  [ErrorCode.MISSING_REQUIRED_FIELD]: '缺少必填字段',
  [ErrorCode.INVALID_FORMAT]: '格式不正确',
  [ErrorCode.PARAMETER_TOO_LONG]: '参数长度超出限制',
  [ErrorCode.PARAMETER_TOO_SHORT]: '参数长度不足',
  [ErrorCode.INVALID_DATE_FORMAT]: '日期格式不正确',
  [ErrorCode.INVALID_NUMBER_FORMAT]: '数字格式不正确',
  [ErrorCode.DUPLICATE_ENTRY]: '数据已存在',
  [ErrorCode.RESOURCE_NOT_FOUND]: '资源不存在',
  [ErrorCode.OPERATION_FAILED]: '操作失败',

  // 认证授权错误
  [ErrorCode.LOGIN_REQUIRED]: '请先登录',
  [ErrorCode.INVALID_TOKEN]: '无效的访问令牌',
  [ErrorCode.TOKEN_EXPIRED]: '访问令牌已过期',
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ErrorCode.ACCOUNT_DISABLED]: '账号已禁用',
  [ErrorCode.ACCOUNT_LOCKED]: '账号已锁定',

  // 盒型业务错误
  [ErrorCode.BOX_NAME_EXISTS]: '盒型名称已存在',
  [ErrorCode.BOX_NOT_FOUND]: '盒型不存在',
  [ErrorCode.BOX_CANNOT_DELETE_PUBLISHED]: '已发布的盒型不能删除',
  [ErrorCode.BOX_INVALID_TYPE]: '盒型类型无效',
  [ErrorCode.BOX_PROCESSING_FEE_INVALID]: '加工费设置无效',
  [ErrorCode.BOX_ATTRIBUTE_INVALID]: '盒型属性设置无效',
  [ErrorCode.BOX_PART_INVALID]: '盒型部件设置无效',
  [ErrorCode.BOX_FORMULA_INVALID]: '盒型公式设置无效',
  [ErrorCode.BOX_IMAGE_INVALID]: '盒型图片格式无效',
  [ErrorCode.BOX_IMAGE_TOO_LARGE]: '盒型图片尺寸过大',

  // 自定义公式业务错误
  [ErrorCode.FORMULA_NAME_EXISTS]: '公式名称已存在',
  [ErrorCode.FORMULA_NOT_FOUND]: '公式不存在',
  [ErrorCode.FORMULA_EXPRESSION_INVALID]: '公式表达式无效',
  [ErrorCode.FORMULA_CANNOT_DELETE_IN_USE]: '公式正在使用中，无法删除',
  [ErrorCode.FORMULA_ATTRIBUTE_INVALID]: '公式属性设置无效',
  [ErrorCode.FORMULA_EVALUATION_FAILED]: '公式计算失败',

  // 材料业务错误
  [ErrorCode.MATERIAL_NAME_EXISTS]: '材料名称已存在',
  [ErrorCode.MATERIAL_NOT_FOUND]: '材料不存在',
  [ErrorCode.MATERIAL_PRICE_INVALID]: '材料价格设置无效',
  [ErrorCode.MATERIAL_UNIT_INVALID]: '材料单位设置无效',
  [ErrorCode.MATERIAL_CATEGORY_INVALID]: '材料分类无效',
  [ErrorCode.MATERIAL_CANNOT_DELETE_IN_USE]: '材料正在使用中，无法删除',
  [ErrorCode.MATERIAL_SPECIFICATION_INVALID]: '材料规格设置无效',

  // 系统错误
  [ErrorCode.DATABASE_ERROR]: '数据库操作失败',
  [ErrorCode.FILE_SYSTEM_ERROR]: '文件系统操作失败',
  [ErrorCode.NETWORK_ERROR]: '网络连接失败',
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: '外部服务调用失败',
  [ErrorCode.MEMORY_ERROR]: '内存不足',
  [ErrorCode.TIMEOUT_ERROR]: '操作超时',
  [ErrorCode.UNKNOWN_ERROR]: '未知错误',
};

// 错误工具函数
export class ApiError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: any;

  constructor(
    code: ErrorCode,
    message?: string,
    details?: any,
    statusCode?: number
  ) {
    super(message || ErrorMessages[code]);
    this.code = code;
    this.details = details;
    this.statusCode = statusCode || 200
    this.name = 'ApiError';
  }

  toJSON() {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
    };
  }
}

// 便捷函数
export function getErrorMessage(code: ErrorCode): string {
  return ErrorMessages[code] || ErrorMessages[ErrorCode.UNKNOWN_ERROR];
}

export function createApiError(
  code: ErrorCode,
  message?: string,
  details?: any
): ApiError {
  return new ApiError(code, message, details);
}

export function isBusinessError(code: ErrorCode): boolean {
  return code >= 1000 && code < 6000;
}

export function isSystemError(code: ErrorCode): boolean {
  return code >= 5000;
} 